# Ultralytics YOLO 🚀, AGPL-3.0 license
# DIUx xView 2018 Challenge https://challenge.xviewdataset.org by U.S. National Geospatial-Intelligence Agency (NGA)
# --------  DOWNLOAD DATA MANUALLY and jar xf val_images.zip to 'datasets/xView' before running train command!  --------
# Documentation: https://docs.ultralytics.com/datasets/detect/xview/
# Example usage: yolo train data=xView.yaml
# parent
# ├── ultralytics
# └── datasets
#     └── xView  ← downloads here (20.7 GB)

# Train/val/val sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]

train: D:\Local-yolo11\ultralytics-main\ultralytics-main\ultralytics\cfg\datasets\DUO\images\train # train images (relative to 'path') 90% of 847 train images
val: D:\Local-yolo11\ultralytics-main\ultralytics-main\ultralytics\cfg\datasets\DUO\images\val # train images (relative to 'path') 10% of 847 train images

# Classes
nc: 4
names:
  0: Holothurian
  1: Echinus
  2: Scallop
  3: Starfish

