# COCO 2017 dataset http://cocodataset.org

# download command/URL (optional)
download: bash ./scripts/get_coco.sh

# train and val data as 1) directory: path/images/, 2) file: path/images.txt, or 3) list: [path1/images/, path2/images/]
train: D:\Local-yolo11\ultralytics-main\ultralytics-main\ultralytics\cfg\datasets\RTTSQ\RTTS\images\train  # 118287 images
val: D:\Local-yolo11\ultralytics-main\ultralytics-main\ultralytics\cfg\datasets\RTTSQ\RTTS\images\val  # 5000 images
test: D:\Local-yolo11\ultralytics-main\ultralytics-main\ultralytics\cfg\datasets\RTTSQ\RTTS\images\val  # 20288 of 40670 images, submit to https://competitions.codalab.org/competitions/20794

# number of classes
nc: 5

# class names
names: [ 'person', 'car', 'bus','bicycle', 'motorcycle' ]
