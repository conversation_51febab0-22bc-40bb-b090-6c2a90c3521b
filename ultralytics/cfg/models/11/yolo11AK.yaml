# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 5 # number of classes
scales:  # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11AK.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # summary: 319 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # summary: 319 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512] # summary: 409 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512] # summary: 631 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512] # summary: 631 layers, 56966176 parameters, 56966160 gradients, 196.0 GFLOPs

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
 # - [-1, 1, Dehaze<PERSON>, [3, 64]]  # 使用去雾模块
 # - [-1, 1, Conv, [ 3, 64, 2]]
 # - [-1, 1, FFA, []]
 #- [-1, 1, TFAM, [64,2]]
 #- [-1, 1, FEM,  [64]]

  #- [ -1, 1, Conv, [ 64, 3, 2]]
  #- [-1, 1, Conv, [32, 3, 2 ]]


#  - [-1, 1, DehazeNet, [3, 64]]
#  - [-1, 1, Conv, [64, 3, 2]]  # 接下来层的输入通道应该是8
#  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
#  - [-1, 2, C3k2, [256, False, 0.25]]
#  - [-1, 1, FEM,  [256]]
#  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
#  - [-1, 2, C3k2, [512, False, 0.25]]
#  - [-1, 1, FEM,  [128]]
#  - [-2, 1, Conv, [512, 3, 2]] # 5-P4/16
#  - [-1, 2, C3k2, [512, True]]
#  - [-1, 1, FEM,  [128]]
#  - [-2, 1, Conv, [1024, 3, 2]] # 7-P5/32
#  - [-1, 2, C3k2, [1024, True]]
#  - [-1, 1, FEM,  [256]]
#  - [-1, 1, SPPF, [1024, 5]] # 9
#  - [-1, 2, C2PSA_MLCA, [1024]] # 10


  - [-1, 1, DehazeFFA, [3, 64]]
  - [-1, 1, Conv, [64, 3, 2]]  # 接下来层的输入通道应该是8
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]
  - [-1, 1, FEM,  [128]]
  - [-1, 1, Conv, [512, 3, 2]] # 5-P4/16zg
  - [-1, 2, C3k2, [512, True]]
  - [-1, 1, FEM,  [128]]
  - [-1, 1, Conv, [1024, 3, 2]] # 7-P5/32zg
  - [-1, 2, C3k2, [1024, True]]
  - [-1, 1, FEM,  [256]]
  - [-1, 1, SPPF, [1024, 5]] # 9
  #- [-1, 2, C2PSA_MLCA, [1024]] # 10
  - [-1, 2, C2PSA, [1024]]

# YOLO11n head
head:

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 7], 1, Concat, [1]] # cat backbone P4
  - [-1, 2, C3k2, [512, False]] # 13

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 5], 1, Concat, [1]] # cat backbone P3
  - [-1, 2, C3k2, [256, False]] # 16 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 15], 1, Concat, [1]] # cat head P4
  - [-1, 2, C3k2, [512, False]] # 19 (P4/16-medium)

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 12], 1, Concat, [1]] # cat head P5
  - [-1, 2, C3k2, [1024, True]] # 22 (P5/32-large)

  - [20, 1, SCAM, [ ]]
  - [23, 1, SCAM, [ ]] # 25
  - [26, 1, SCAM, [ ]]

  - [[27, 28, 29], 1, Detect, [nc]] # Detect(P3, P4, P5)
# YOLO11n head

#head:

#  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
#  - [[-1, 8], 1, Concat, [1]] # cat backbone P4
#  - [-1, 2, C3k2, [512, False]] # 13
#
#  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
#  - [[-1, 6], 1, Concat, [1]] # cat backbone P3
#  - [-1, 2, C3k2, [256, False]] # 16 (P3/8-small)
#
#  - [-1, 1, Conv, [256, 3, 2]]
#  - [[-1, 16], 1, Concat, [1]] # cat head P4
#  - [-1, 2, C3k2, [512, False]] # 19 (P4/16-medium)
#
#  - [-1, 1, Conv, [512, 3, 2]]
#  - [[-1, 13], 1, Concat, [1]] # cat head P5
#  - [-1, 2, C3k2, [1024, True]] # 22 (P5/32-large)
#
#  - [21, 1, SCAM, [ ]] # 24
#  - [24, 1, SCAM, [ ]] # 25
#  - [27, 1, SCAM, [ ]]
#  - [[28, 29, 30], 1, Detect, [nc]] # Detect(P3, P4, P5)


 #- [[17, 20, 23], 1, Detect, [nc]] # Detect(P3, P4, P5)

#  - [-1, 1, DehazeNet, [3, 64]]
#  - [-1, 1, MVP, [ ]]
#  - [-1, 1, Conv, [64, 3, 2]]  # 接下来层的输入通道应该是8
#  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
#  - [-1, 2, C3k2, [256, False, 0.25]]
#  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
#  - [-1, 2, C3k2, [512, False, 0.25]]
#  - [-1, 1, FEM,  [128]]
#  - [-2, 1, Conv, [512, 3, 2]] # 5-P4/16
#  - [-1, 2, C3k2, [512, True]]
#  - [-1, 1, FEM,  [128]]
#  - [-2, 1, Conv, [1024, 3, 2]] # 7-P5/32
#  - [-1, 2, C3k2, [1024, True]]
#  - [-1, 1, FEM,  [256]]
#  - [-1, 1, SPPF, [1024, 5]] # 9
#  - [-1, 2, C2PSA, [1024]] # 10
#
## YOLO11n head
#head:
#
#  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
#  - [[-1, 8], 1, Concat, [1]] # cat backbone P4
#  - [-1, 2, C3k2, [512, False]] # 13
#
#  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
#  - [[-1, 6], 1, Concat, [1]] # cat backbone P3
#  - [-1, 2, C3k2, [256, False]] # 16 (P3/8-small)
#
#  - [-1, 1, Conv, [256, 3, 2]]
#  - [[-1, 16], 1, Concat, [1]] # cat head P4
#  - [-1, 2, C3k2, [512, False]] # 19 (P4/16-medium)
#
#  - [-1, 1, Conv, [512, 3, 2]]
#  - [[-1, 13], 1, Concat, [1]] # cat head P5
#  - [-1, 2, C3k2, [1024, True]] # 22 (P5/32-large)
#
#  - [21, 1, SCAM, [ ]] # 24
#  - [24, 1, SCAM, [ ]] # 25
#  - [27, 1, SCAM, [ ]]
#  - [[28, 29, 30], 1, Detect, [nc]] # Detect(P3, P4, P5)


